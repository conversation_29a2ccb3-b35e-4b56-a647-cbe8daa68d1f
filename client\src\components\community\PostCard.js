import React from 'react';
import UnifiedPostCard from './UnifiedPostCard';

/**
 * PostCard component - Wrapper for UnifiedPostCard to maintain compatibility
 */
const PostCard = (props) => {
  // Handle comment toggle
  const handleToggleComments = () => {
    if (props.onComment && props.post) {
      props.onComment(props.post.id);
    }
  };

  return (
    <UnifiedPostCard
      {...props}
      onToggleComments={handleToggleComments}
    />
  );
};

export default PostCard;
