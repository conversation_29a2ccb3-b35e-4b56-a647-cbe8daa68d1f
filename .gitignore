# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.render

# Build outputs
build/
dist/
*.tgz
*.tar.gz

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files and platform-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows specific
*.lnk
desktop.ini
$RECYCLE.BIN/

# macOS specific
.AppleDouble
.LSOverride
.DocumentRevisions-V100
.fseventsd
.TemporaryItems
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux specific
*~
.directory
.Trash-*

# Cross-platform editor backup files
*.bak
*.backup
*.tmp
*.temp

# Logs
logs
*.log

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Docker - Keep .dockerignore files (they define Docker build context)
# .dockerignore files should be version controlled for consistent builds

# But keep docker-compose files and Dockerfiles
!Dockerfile*
!docker-compose*.yml
!docker-compose*.yaml
!.dockerignore

# Temporary files
tmp/
temp/

# Documentation and test files (generated)
*_GUIDE.md
*_FIX.md
*_SETUP.md
*_DEPLOYMENT*.md
*_INTEGRATION.md
*_SOLUTION*.md

# Generated test files (not deployment scripts)
test-*.js
test-*.html
*-debug.js
*-test.js
check-*.js

# Keep all deployment scripts - they are needed for cross-platform compatibility
# All deployment scripts should be version controlled
!deploy-*.sh
!deploy-*.bat
!deploy-*.ps1
!setup-*.sh
!setup-*.bat
!setup-*.ps1

# Keep all essential scripts in scripts/ directory
!scripts/deploy-*.sh
!scripts/deploy-*.bat
!scripts/deploy-*.ps1
!scripts/setup-*.sh
!scripts/setup-*.bat
!scripts/setup-*.ps1
!scripts/deploy-firebase-rules.js
!scripts/deploy-optimization-indexes.js

# Keep Kubernetes deployment scripts
!k8s/deploy-*.sh
!k8s/deploy-*.bat
!k8s/deploy-*.ps1
!k8s/cleanup.sh

# API keys and sensitive data
*.key
*.pem
service-account-*.json
config/firebase-service-account.json
firebase-service-account.json

# Security - Database files that may contain secrets
services/phishtank-service/data/phishtank-database.json
services/criminalip-service/data/*.json
*.database.json
*-database.json

# Build artifacts and generated files (keep these ignored)
*.zip
*.tar
*.rar
*.tgz
*.tar.gz

# Build outputs
build/
dist/
out/
target/

# But keep essential build and monitoring scripts
!build-*.sh
!build-*.bat
!build-*.ps1
!start-monitoring*.sh
!start-monitoring*.bat
!start-monitoring*.ps1
!stop-*.sh
!stop-*.bat
!stop-*.ps1
!check-*.sh
!check-*.bat
!check-*.ps1
!fix-*.sh
!fix-*.bat
!fix-*.ps1
!install-*.sh
!install-*.bat
!install-*.ps1
!prepare-*.sh
!prepare-*.bat
!prepare-*.ps1

# Editor files
*.sublime-*
.atom/

# Local development and configuration
.env.backup
.env.example.local

# Keep configuration templates for cross-platform setup
!.env.template
!.env.example
!.env.sample
!docker-compose*.yml
!nginx.conf
!Makefile

# Keep render deployment configs
!render-*.yaml
!render-*.yml

# Old monolith files (deprecated after microservices migration)
server/
functions/
firebase.json
firestore.rules
firestore.indexes.json
firestore.indexes.new.json
restart-all.bat
restart-all.js

# Backup directories
backup-monolith/
backup-monolith-manual/
