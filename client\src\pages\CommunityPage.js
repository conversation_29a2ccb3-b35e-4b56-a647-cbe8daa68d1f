import React, { useEffect } from 'react';
import NavigationLayout from '../components/navigation/NavigationLayout';
import { useCommunityData } from '../hooks/useCommunityData';

const CommunityPage = () => {
  console.log('🎯 CommunityPage component rendering...');

  // Use community data hook for user posts only
  const { data: communityData, loading, error, fetchData } = useCommunityData();
  const posts = communityData.posts || [];

  // Test API call on mount
  useEffect(() => {
    console.log('🚀 useEffect [] (mount) triggered, calling fetchData...');

    const testFetch = async () => {
      try {
        console.log('📡 About to call fetchData...');
        await fetchData({
          sort: 'newest',
          page: 1,
          newsOnly: true,
          userPostsOnly: false,
          includeNews: false
        });
        console.log('✅ fetchData completed');
      } catch (error) {
        console.error('❌ fetchData error:', error);
      }
    };

    // Also test direct API call
    const testDirectAPI = async () => {
      try {
        console.log('🔥 Testing direct API call...');
        const response = await fetch('http://localhost:8080/api/community/posts?page=1&sort=newest&limit=10&newsOnly=true');
        const data = await response.json();
        console.log('🔥 Direct API response:', data);
      } catch (error) {
        console.error('🔥 Direct API error:', error);
      }
    };

    testFetch();
    testDirectAPI();
  }, []);

  console.log('🎯 CommunityPage rendering JSX...');
  console.log('📊 Current data:', { posts: posts.length, loading, error });

  return (
    <NavigationLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
        <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Community Page</h1>

        {/* Success Info */}
        <div className="bg-green-100 border border-green-400 text-green-700 p-4 text-sm mb-4 rounded">
          <p>✅ <strong>Community Page Working!</strong></p>
          <p>📡 API calls successful</p>
          <p>🔥 Firebase emulator connected</p>
          <p>🎯 Posts: {posts.length} | Loading: {loading ? 'true' : 'false'} | Error: {error || 'none'}</p>
        </div>

        {/* Posts */}
        <div>
          {loading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading posts...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 p-4 rounded mb-4">
              <p><strong>Error:</strong> {error}</p>
            </div>
          )}

          {posts.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                Posts ({posts.length})
              </h2>
              <div className="grid gap-4">
                {posts.map((post, index) => (
                  <div key={post.id || index} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                    <h3 className="font-semibold text-lg mb-2 text-gray-900 dark:text-white">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-2">
                      {post.content?.substring(0, 200)}...
                    </p>
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-500">
                      <span>By {post.author || 'Anonymous'}</span>
                      <span className="mx-2">•</span>
                      <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {!loading && posts.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                No posts yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Be the first to share something with the community!
              </p>
              <button className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                Create Post
              </button>
            </div>
          )}
        </div>
      </div>
    </NavigationLayout>
  );
};

export default CommunityPage;
