import React, { useEffect } from 'react';
import NavigationLayout from '../components/navigation/NavigationLayout';
import { useCommunityData } from '../hooks/useCommunityData';

const CommunityPage = () => {
  console.log('🎯 CommunityPage component rendering...');

  // Use community data hook for user posts only
  const { data: communityData, loading, error, fetchData } = useCommunityData();
  const posts = communityData.posts || [];

  // Test API call on mount
  useEffect(() => {
    console.log('🚀 useEffect [] (mount) triggered, calling fetchData...');

    const testFetch = async () => {
      try {
        console.log('📡 About to call fetchData...');
        await fetchData({
          sort: 'newest',
          page: 1,
          newsOnly: true,
          userPostsOnly: false,
          includeNews: false
        });
        console.log('✅ fetchData completed');
      } catch (error) {
        console.error('❌ fetchData error:', error);
      }
    };

    // Also test direct API call
    const testDirectAPI = async () => {
      try {
        console.log('🔥 Testing direct API call...');
        const response = await fetch('http://localhost:8080/api/community/posts?page=1&sort=newest&limit=10&newsOnly=true');
        const data = await response.json();
        console.log('🔥 Direct API response:', data);
      } catch (error) {
        console.error('🔥 Direct API error:', error);
      }
    };

    testFetch();
    testDirectAPI();
  }, []);

  console.log('🎯 CommunityPage rendering JSX...');
  console.log('📊 Current data:', { posts: posts.length, loading, error });

  return (
    <NavigationLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
        <h1 className="text-2xl font-bold mb-4">Community Page</h1>
        
        {/* Debug Info */}
        <div className="bg-yellow-100 p-4 text-sm mb-4">
          <p>🎯 CommunityPage Debug:</p>
          <p>Posts: {posts.length}</p>
          <p>Loading: {loading ? 'true' : 'false'}</p>
          <p>Error: {error || 'none'}</p>
        </div>

        {/* Posts */}
        <div>
          {loading && <p>Loading...</p>}
          {error && <p className="text-red-500">Error: {error}</p>}
          {posts.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-2">Posts ({posts.length})</h2>
              {posts.map((post, index) => (
                <div key={post.id || index} className="bg-white p-4 mb-2 rounded shadow">
                  <h3 className="font-medium">{post.title}</h3>
                  <p className="text-sm text-gray-600">{post.content?.substring(0, 100)}...</p>
                </div>
              ))}
            </div>
          )}
          {!loading && posts.length === 0 && <p>No posts found</p>}
        </div>
      </div>
    </NavigationLayout>
  );
};

export default CommunityPage;
